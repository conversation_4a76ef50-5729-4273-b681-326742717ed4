const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { fileURLToPath } = require('url');
const { dirname } = require('path');

// Get __dirname and __filename for CommonJS
// const __filename = __filename;
// const __dirname = __dirname;

// We'll import logger dynamically when needed

// Function to get the current date in YYYY-MM-DD format
const getCurrentDate = () => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
};

// Function to get the database file path
const getDBFilePath = () => {
  const dbDir = path.join(__dirname, 'data');
  // Ensure the data directory exists
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  return path.join(dbDir, `ticks-${getCurrentDate()}.sqlite`);
};

/**
 * Fetches trading pair data from the SQLite database.
 * @param {string} symbol - The trading pair symbol (e.g., 'btcusdt').
 * @returns {Promise<{lastRecordTime: number | null, totalRecords: number, closedKlineCount: number}>}
 */
const getTradingPairData = async (symbol) => {
  // Dynamic import for logger
  const logger = (await import('./logger.js')).default;

  const dbFile = getDBFilePath();
  let db;

  try {
    db = new Database(dbFile);
    const tableName = symbol.toLowerCase();

    // Check if table exists
    const tableExists = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
    if (!tableExists) {
      logger.warn(`Table for symbol "${symbol}" does not exist in ${dbFile}.`);
      return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
    }

    // Query for last record time (kline_end_time)
    const lastRecordQuery = db.prepare(`SELECT kline_end_time, timestamp FROM "${tableName}" ORDER BY kline_end_time DESC LIMIT 1`);
    const lastRecordResult = lastRecordQuery.get();
    const lastRecordTime = lastRecordResult ? lastRecordResult.kline_end_time : null;
    const lastTimestamp = lastRecordResult ? lastRecordResult.timestamp : null;

    // Query for total number of records
    const totalRecordsQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
    const totalRecordsResult = totalRecordsQuery.get();
    const totalRecords = totalRecordsResult ? totalRecordsResult.count : 0;

    // Query for number of closed klines
    const closedKlineQuery = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}" WHERE kline_is_closed = 1`);
    const closedKlineResult = closedKlineQuery.get();
    const closedKlineCount = closedKlineResult ? closedKlineResult.count : 0;

    return { lastRecordTime, totalRecords, closedKlineCount, lastTimestamp };

  } catch (err) {
    logger.error(`Error fetching data for symbol ${symbol} from ${dbFile}: ${err.message}`);
    return { lastRecordTime: null, totalRecords: 0, closedKlineCount: 0 };
  } finally {
    if (db) {
      db.close();
    }
  }
};

module.exports = {
  getTradingPairData,
  getDBFilePath,
  getCurrentDate,
};
