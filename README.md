# Binance Futures Tick Data Collector

A high-performance, scalable system for collecting tick data from Binance Futures WebSocket API.

## 🚀 Features

- **Real-time Data Collection**: Connects to Binance Futures WebSocket API for live tick data
- **Multi-Connection Support**: Distributes 100+ symbols across multiple WebSocket connections
- **Self-Healing Connections**: Automatic reconnection with exponential backoff
- **Redis Queue System**: Uses BullMQ for reliable data distribution
- **Multi-Core Processing**: Leverages all CPU cores with Node.js worker threads
- **SQLite Storage**: Efficient batch writes to daily database files
- **Health Monitoring**: HTTP endpoints for system status and metrics
- **Comprehensive Logging**: Daily rotating log files with detailed error tracking

## 🏗️ System Architecture

```text
┌─────────────────┐    ┌──────────────┐    ┌────────────────┐
│  Tick Collector │───▶│ Redis Queue  │───▶│  Tick Workers  │
│ (WebSocket API) │    │ (BullMQ)     │    │ (Multi-core)   │
└─────────────────┘    └──────────────┘    └────────────────┘
                              │                    │
                              ▼                    ▼
                       ┌──────────────┐    ┌────────────────┐
                       │ Health       │    │ SQLite Storage │
                       │ Server       │    │ (Daily files)  │
                       └──────────────┘    └────────────────┘
```

## 🛠️ Technology Stack

- **Node.js** (v18+) - Runtime environment
- **Redis/BullMQ** - Queue management system
- **SQLite/better-sqlite3** - High-performance database storage
- **WebSocket** - Real-time data streaming
- **Express.js** - Health monitoring endpoints

## 🖥️ Hardware Requirements

- **Mac Studio M1 Max** (10 core CPU, 32 GB RAM recommended)
- **SSD Storage** (Minimum 256 GB, 500+ GB recommended)
- **Redis Server** running on localhost:6379

## 📦 Installation

1. Install dependencies:
```bash
npm install
```

2. Ensure Redis is running on localhost:6379

## ⚙️ Configuration

The system is pre-configured with 100 trading pairs across 2 WebSocket connections in `symbols.json`:
- Connection 1: 50 major pairs (BTCUSDT, ETHUSDT, etc.)
- Connection 2: 50 alt pairs (SOLUSDT, ADAUSDT, etc.)

### Custom Pair Count Configuration

You can control the number of pairs used per WebSocket connection through the `config.json` file:

```json
{
  "websocket": {
    "pairsPerConnection": 50
  }
}
```

This setting limits how many pairs are actually subscribed to from each connection defined in `symbols.json`. By default, it uses 50 pairs per connection, but you can adjust this number based on your needs or system resources.

For example, setting `pairsPerConnection` to 25 will subscribe to only the first 25 pairs from each connection, reducing the total number of subscribed pairs from 100 to 50.

## ▶️ Usage

Start the system:
```bash
npm start
```

For development with auto-restart:
```bash
npm run dev
```

## 🌐 Endpoints

- **Health Check**: http://localhost:3000/health
- **System Status**: http://localhost:3000/status
- **Prometheus Metrics**: http://localhost:3000/metrics

## 📊 Data Flow

1. **Tick Collector** connects to Binance Futures WebSocket API
2. **Incoming tick data** is parsed and validated
3. **Data is queued** in Redis for reliable distribution
4. **Tick Workers** consume from queue using all CPU cores
5. **Data is buffered** and written to SQLite in batches
6. **Daily database files** are automatically created (`ticks-YYYY-MM-DD.sqlite`)
7. **Health server** provides real-time monitoring endpoints

## 🔍 Monitoring & Observability

- **WebSocket Connection Status**: Tracks connection health and reconnection attempts
- **Redis Queue Metrics**: Monitors waiting, active, completed, and failed jobs
- **Processing Throughput**: Tracks ticks per second and system performance
- **Memory Usage**: Monitors heap usage and system resources
- **Daily Log Files**: Comprehensive logging in `logs/YYYY-MM-DD.log`

## 📁 File Structure

- `tick-collector.js`: Collects tick data from Binance WebSocket
- `tick-worker.js`: Processes queue data and writes to SQLite
- `queue.js`: Redis connection and queue management
- `sqlite-writer.js`: SQLite operations and batch insert functions
- `websocket-monitor.js`: WebSocket connection monitoring and self-healing
- `health-server.js`: Express.js health endpoint and status reporting
- `logger.js`: Logging functionality with daily rotating files
- `symbols.json`: Trading pair configuration
- `index.js`: Main application entry point

## 🛡️ Error Handling & Reliability

- **Automatic Reconnection**: WebSocket connections automatically reconnect on failure
- **Exponential Backoff**: Progressive delay between reconnection attempts
- **Queue Persistence**: Redis stores jobs to prevent data loss
- **Graceful Shutdown**: Proper cleanup of connections and data flushing
- **Error Recovery**: Failed jobs are retried with exponential backoff

## 📈 Performance Optimization

- **Batch Writing**: SQLite inserts are batched for efficiency
- **Buffer Management**: Per-symbol buffers with configurable flush intervals
- **Multi-Core Processing**: One worker thread per CPU core
- **Connection Pooling**: Efficient Redis connection management
- **Memory Efficient**: Streaming data processing with minimal memory footprint

## 📅 Daily Database Rotation

- Daily SQLite files: `ticks-YYYY-MM-DD.sqlite`
- Automatic file creation at midnight UTC
- Previous day files can be archived to S3 or other storage (optional)

## 🧪 Testing

Run tests:
```bash
npm test
```

## 🚀 Production Deployment

For production deployment, consider using PM2:
```bash
npm install -g pm2
pm2 start index.js --name binance-tick-collector
```

## 📞 Support

For issues and feature requests, please open an issue on GitHub.
