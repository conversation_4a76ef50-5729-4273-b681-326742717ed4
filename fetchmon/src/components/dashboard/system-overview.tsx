// System overview cards component
import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Server, 
  Database, 
  Activity, 
  HardDrive,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { SystemStatus, MetricsData } from '@/lib/types';

interface SystemOverviewProps {
  status: SystemStatus | null;
  metrics: MetricsData | null;
  loading: boolean;
}

export function SystemOverview({ status, metrics, loading }: SystemOverviewProps) {
  // Calculate memory usage percentage
  const memoryUsage = metrics?.processMemory 
    ? Math.round((metrics.processMemory.heapUsed / metrics.processMemory.heapTotal) * 100) 
    : 0;
  
  // Calculate queue load percentage
  const queueTotal = metrics?.tickQueueJobs 
    ? metrics.tickQueueJobs.waiting + metrics.tickQueueJobs.active + metrics.tickQueueJobs.failed
    : 0;
  
  const queueLoad = metrics?.tickQueueJobs && queueTotal > 0
    ? Math.round((metrics.tickQueueJobs.active / queueTotal) * 100)
    : 0;

  // System status badge
  const systemStatus = status?.system?.redis?.connected ? 'healthy' : 'unhealthy';
  
  // Calculate total connections
  const connections = status?.system?.tickCollector 
    ? Object.keys(status.system.tickCollector).length 
    : 0;
  
  // Calculate active connections
  const activeConnections = status?.system?.tickCollector
    ? Object.values(status.system.tickCollector).filter(conn => conn.isConnected).length
    : 0;


  const [serverStatusLoading, setserverStatusLoading] = useState(false);
  const [serverStatus, setserverStatus] = useState('error');
  const [loadingMessage, setLoadingMessage] = useState('checking');
  
  const pingServer = async () => {
    try {
      console.log('pingServer ');
      setserverStatusLoading(true);
      setLoadingMessage('checking');
      const response = await fetch('/api/server/ping');
      // console.log('response from ping', response);
      if (!response.ok) {
        throw new Error('Ping failed');
      }
      setserverStatus('healthy')
      console.log('Ping successful');
    } catch {
      // console.error('Ping error:', error);
      setserverStatus('unhealthy');
      // Implement restart logic here
      console.log('Start process required.');
      // Call the restart API endpoint

    } finally {
      setserverStatusLoading(false);
    }
  };
  const startStopServer = async (type: 'start' | 'stop') => {
    try {
      setserverStatusLoading(true);
      setLoadingMessage(type === 'start' ? 'starting' : 'stopping');
      const endpoint = type === 'start' ? '/api/server/restart' : '/api/server/stop';

      console.log(`${type} operation started...`);
      const response = await fetch(endpoint, { method: 'POST' });

      if (!response.ok) {
        throw new Error(`${type} failed with status: ${response.status}`);
      }

      console.log(`${type} API call successful`);

      // For start operation, wait a bit longer before pinging
      if (type === 'start') {
        setLoadingMessage('waiting for server');
        console.log('Waiting for server to fully start...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

      // pingServer() kendi loading state'ini yönetiyor
      await pingServer();
    } catch (error) {
      console.error(`${type} error:`, error);
      setserverStatusLoading(false);
    }
  };


  useEffect(() => {
    pingServer();
  }, []); // Empty dependency array ensures this runs only once on mount
  

  return (
    <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-5">
      {/* Server Status Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Server Status</CardTitle>
          {serverStatus === 'healthy' ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertTriangle className="h-4 w-4 text-red-500" />
          )}
        </CardHeader>
        
        <CardContent className='flex justify-between'>
          <div>
            <div className="text-2xl font-bold">
              {serverStatusLoading ? (
                <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
              ) : serverStatus === 'healthy' ? (
                'Healthy'
              ) : (
                'Unhealthy'
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {serverStatusLoading ? (
                <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-1" />
              ) : serverStatus === 'healthy' ? (
                'All systems operational'
              ) : (
                'System issues detected'
              )}
            </p>
          </div>
          <div>
            {serverStatusLoading ? (
              loadingMessage
            ) : serverStatus === 'healthy' ? (
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  if (window.confirm('Are you sure you want to stop the server?')) {
                    await startStopServer('stop');
                  }
                }}
              >
                Stop
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  if (window.confirm('Are you sure you want to start the server?')) {
                    await startStopServer('start');
                  }
                }}
              >
                Start
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
{/* System Status Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Status</CardTitle>
          {systemStatus === 'healthy' ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertTriangle className="h-4 w-4 text-red-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
            ) : systemStatus === 'healthy' ? (
              'Healthy'
            ) : (
              'Unhealthy'
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            {loading ? (
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-1" />
            ) : systemStatus === 'healthy' ? (
              'All systems operational'
            ) : (
              'System issues detected'
            )}
          </p>
        </CardContent>
      </Card>

      {/* Memory Usage Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
          <Server className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-6 w-16 bg-gray-200 rounded animate-pulse" />
            ) : (
              `${memoryUsage}%`
            )}
          </div>
          <Progress value={memoryUsage} className="mt-2" />
          <div className="text-xs text-muted-foreground mt-2">
            {loading ? (
              <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
            ) : (
              `${(metrics?.processMemory?.heapUsed || 0) / 1024 / 1024 | 0} MB / ${(metrics?.processMemory?.heapTotal || 0) / 1024 / 1024 | 0} MB`
            )}
          </div>
        </CardContent>
      </Card>

      {/* Queue Load Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Queue Load</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-6 w-16 bg-gray-200 rounded animate-pulse" />
            ) : (
              `${queueLoad}%`
            )}
          </div>
          <Progress value={queueLoad} className="mt-2" />
          <p className="text-xs text-muted-foreground mt-2">
            {loading ? (
              <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
            ) : (
              `${metrics?.tickQueueJobs?.active || 0} active jobs`
            )}
          </p>
        </CardContent>
      </Card>

      {/* Connections Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Connections</CardTitle>
          {activeConnections === connections ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-yellow-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-6 w-16 bg-gray-200 rounded animate-pulse" />
            ) : (
              `${activeConnections}/${connections}`
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            {loading ? (
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-1" />
            ) : activeConnections === connections ? (
              'All connections active'
            ) : (
              `${connections - activeConnections} connections down`
            )}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
