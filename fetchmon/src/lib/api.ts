// API client for the Binance Tick Collector

import {
  SystemHealth,
  SystemStatus,
  MetricsData,
  TradingPair,
  TradingPairsApiResponse
} from '@/lib/types';

const API_BASE_URL = 'http://localhost:3000';

export class ApiClient {
  private async fetchFromApi<T>(endpoint: string): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        cache: 'no-store',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error fetching from ${endpoint}:`, error);
      throw error;
    }
  }

  // Health endpoints
  async getHealth(): Promise<SystemHealth> {
    return this.fetchFromApi<SystemHealth>('/health');
  }

  async getStatus(): Promise<SystemStatus> {
    return this.fetchFromApi<SystemStatus>('/status');
  }

  async getMetrics(): Promise<MetricsData> {
    // Parse Prometheus metrics format
    const response = await fetch(`${API_BASE_URL}/metrics`, {
      cache: 'no-store',
    });

    if (!response.ok) {
      throw new Error(`Metrics request failed with status ${response.status}`);
    }

    const text = await response.text();
    return this.parseMetrics(text);
  }

  private parseMetrics(metricsText: string): MetricsData {
    const lines = metricsText.split('\n');
    const metrics: MetricsData = {
      tickQueueJobs: {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0
      },
      processUptime: 0,
      processMemory: {
        heapUsed: 0,
        heapTotal: 0,
        rss: 0,
        external: 0
      }
    };

    for (const line of lines) {
      if (line.startsWith('tick_queue_jobs{state="waiting"}')) {
        metrics.tickQueueJobs.waiting = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('tick_queue_jobs{state="active"}')) {
        metrics.tickQueueJobs.active = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('tick_queue_jobs{state="completed"}')) {
        metrics.tickQueueJobs.completed = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('tick_queue_jobs{state="failed"}')) {
        metrics.tickQueueJobs.failed = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('tick_queue_jobs{state="delayed"}')) {
        metrics.tickQueueJobs.delayed = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('process_uptime_seconds')) {
        metrics.processUptime = parseFloat(line.split(' ')[1]) || 0;
      } else if (line.startsWith('process_memory_bytes{type="heap_used"}')) {
        metrics.processMemory.heapUsed = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('process_memory_bytes{type="heap_total"}')) {
        metrics.processMemory.heapTotal = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('process_memory_bytes{type="rss"}')) {
        metrics.processMemory.rss = parseInt(line.split(' ')[1]) || 0;
      } else if (line.startsWith('process_memory_bytes{type="external"}')) {
        metrics.processMemory.external = parseInt(line.split(' ')[1]) || 0;
      }
    }

    return metrics;
  }

  // Control endpoints (to be implemented in the fetchers application)
  async startPair(pair: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/pairs/${pair}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to start pair ${pair}`);
      }
    } catch (error) {
      console.error(`Error starting pair ${pair}:`, error);
      throw error;
    }
  }

  async stopPair(pair: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/pairs/${pair}/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to stop pair ${pair}`);
      }
    } catch (error) {
      console.error(`Error stopping pair ${pair}:`, error);
      throw error;
    }
  }

  async getTradingPairsData(): Promise<TradingPairsApiResponse> {
    return this.fetchFromApi<TradingPairsApiResponse>('/pairs');
  }
}

export const apiClient = new ApiClient();
