// fetchmon/src/app/api/server/ping/route.js

import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const pingResponse = await fetch('http://localhost:3000/server/ping');

    if (!pingResponse.ok) {
      // If the ping fails, return a 500 status
      return NextResponse.json({ error: 'Ping failed' }, { status: 500 });
    }

    const data = await pingResponse.json();
    return NextResponse.json(data); // Forward the response from the ping

  } catch (error) {
    // console.error('Ping error:', error);
    return NextResponse.json({ error: 'Ping failed, Server not reachable' }, { status: 500 });
  }
}